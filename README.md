# A股股票数据爬取与可视化分析

## 项目简介

本项目实现了A股股票数据的实时爬取和可视化分析，主要功能包括：

1. **数据爬取**: 在A股交易时间段，爬取5支股票15分钟内的交易价格数据和涨跌率
2. **数据存储**: 将爬取的数据保存到CSV文件
3. **数据可视化**: 绘制涨跌率对比图和价格变化详情图
4. **数据分析**: 生成详细的数据分析报告

## 选择的股票

- 000001.SZ 平安银行
- 600519.SH 贵州茅台
- 002594.SZ 比亚迪
- 000858.SZ 五粮液
- 600036.SH 招商银行

## A股交易时间

工作日 9:00-11:30、13:00-15:00

## 项目结构

```
data_collect/
├── stock_crawler.ipynb     # 主要的Jupyter notebook
├── test_stock_api.py      # 测试股票API连接
├── requirements.txt       # Python依赖包
├── README.md             # 项目说明文档
├── stock_data.csv        # 生成的股票数据文件
├── 股票涨跌率对比图.png    # 涨跌率对比图
└── 股票价格变化详情图.png  # 价格变化详情图
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 测试API连接

首先测试股票API是否可用：

```bash
python test_stock_api.py
```

### 2. 运行主程序

启动Jupyter Notebook：

```bash
jupyter notebook stock_crawler.ipynb
```

然后按顺序执行所有单元格。

### 3. 数据爬取说明

- **交易时间内**: 程序会自动检测当前是否为A股交易时间，如果是，则进行实时数据爬取
- **非交易时间**: 程序会生成模拟数据用于演示和测试

### 4. 输出文件

程序运行完成后会生成以下文件：

- `stock_data.csv`: 包含所有爬取的股票数据
- `股票涨跌率对比图.png`: 5支股票涨跌率变化对比图
- `股票价格变化详情图.png`: 5支股票价格变化详情图

## 数据爬取策略

- **时间间隔**: 15分钟内每隔1分钟爬取一次数据（共16个数据点）
- **同步爬取**: 同一时间点同时爬取5支股票的数据
- **数据内容**: 股票代码、名称、当前价格、昨日收盘价、涨跌率、时间戳

## 可视化图表

### 1. 涨跌率对比图
- 展示5支股票在15分钟内的涨跌率变化趋势
- 使用不同颜色和标记区分不同股票
- 包含零线参考

### 2. 价格变化详情图
- 分别展示每支股票的价格变化情况
- 自动调整Y轴刻度以突出价格变化
- 显示最高价、最低价和波动幅度信息

## 技术特点

- **实时数据**: 使用新浪财经API获取实时股票数据
- **错误处理**: 完善的异常处理机制
- **时间检测**: 自动检测A股交易时间
- **数据验证**: 数据格式验证和清洗
- **可视化**: 专业的图表展示和分析

## 注意事项

1. 确保网络连接正常
2. 在A股交易时间内运行效果最佳
3. 请遵守相关网站的使用条款
4. 数据仅供学习和研究使用

## 转换为PDF

要将notebook转换为PDF，可以使用以下命令：

```bash
jupyter nbconvert --to pdf stock_crawler.ipynb
```

或者在notebook中使用：File -> Download as -> PDF via LaTeX

## 依赖包说明

- `requests`: HTTP请求库，用于API调用
- `pandas`: 数据处理和分析
- `numpy`: 数值计算
- `matplotlib`: 数据可视化
- `jupyter`: Jupyter notebook环境
- `nbconvert`: notebook转换工具
