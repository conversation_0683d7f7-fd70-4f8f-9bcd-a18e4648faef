# 导入必要的库
import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import time
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 定义股票代码和名称
stocks = {
    '000001': '平安银行',
    '600519': '贵州茅台', 
    '002594': '比亚迪',
    '000858': '五粮液',
    '600036': '招商银行'
}

# 数据存储列表
all_data = []

def get_stock_data(stock_code, base_prices=None, minute=0):
    """
    获取股票实时数据
    优先尝试API获取，失败时使用模拟数据
    """
    try:
        # 首先尝试API获取
        if stock_code.startswith('6'):
            market = 'sh'  # 上海证券交易所
        else:
            market = 'sz'  # 深圳证券交易所
        
        # 新浪财经API
        url = f'http://hq.sinajs.cn/list={market}{stock_code}'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=5)
        response.encoding = 'gbk'
        
        if response.status_code == 200 and 'var hq_str_' in response.text:
            data_str = response.text
            data_part = data_str.split('"')[1]
            data_list = data_part.split(',')
            
            if len(data_list) >= 32 and data_list[0]:  # 确保有股票名称
                stock_name = data_list[0]
                current_price = float(data_list[3])  # 当前价格
                yesterday_close = float(data_list[2])  # 昨日收盘价
                
                if current_price > 0 and yesterday_close > 0:  # 确保价格有效
                    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
                    
                    return {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'current_price': current_price,
                        'yesterday_close': yesterday_close,
                        'change_rate': round(change_rate, 2)
                    }
        
        # API失败时使用模拟数据
        print(f"API获取失败，使用模拟数据: {stock_code}")
        return generate_mock_data(stock_code, base_prices, minute)
        
    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}，使用模拟数据")
        return generate_mock_data(stock_code, base_prices, minute)

def generate_mock_data(stock_code, base_prices=None, minute=0):
    """
    生成模拟股票数据
    """
    if base_prices is None:
        base_prices = {'000001': 12.5, '600519': 1680.0, '002594': 280.0, '000858': 180.0, '600036': 45.0}
    
    stock_name = stocks[stock_code]
    base_price = base_prices[stock_code]
    
    # 生成更真实的股票价格波动
    np.random.seed(int(time.time()) + int(stock_code) + minute)
    
    # 基础波动率
    volatility = 0.02  # 2%的波动率
    
    # 生成随机变化
    random_change = np.random.normal(0, volatility)
    
    # 添加一些趋势性
    trend_factor = np.sin(minute * 0.3) * 0.005  # 轻微的周期性趋势
    
    # 计算当前价格
    price_change_rate = random_change + trend_factor
    current_price = base_price * (1 + price_change_rate)
    
    yesterday_close = base_price
    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
    
    return {
        'stock_code': stock_code,
        'stock_name': stock_name,
        'current_price': round(current_price, 2),
        'yesterday_close': yesterday_close,
        'change_rate': round(change_rate, 2)
    }

def check_trading_time():
    """
    检查当前是否为A股交易时间
    A股交易时间：工作日 9:00-11:30、13:00-15:00
    """
    now = datetime.now()
    
    # 检查是否为工作日（周一到周五）
    if now.weekday() >= 5:  # 周六、周日
        return False
    
    current_time = now.time()
    
    # 上午交易时间 9:00-11:30
    morning_start = datetime.strptime('09:00', '%H:%M').time()
    morning_end = datetime.strptime('11:30', '%H:%M').time()
    
    # 下午交易时间 13:00-15:00
    afternoon_start = datetime.strptime('13:00', '%H:%M').time()
    afternoon_end = datetime.strptime('15:00', '%H:%M').time()
    
    return (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end)

# 基础价格设置
base_prices = {'000001': 12.5, '600519': 1680.0, '002594': 280.0, '000858': 180.0, '600036': 45.0}

# 检查当前是否为交易时间
is_trading_time = check_trading_time()

if is_trading_time:
    print("当前为A股交易时间，开始爬取实时数据...")
    sleep_interval = 60  # 实际爬取时等待1分钟
else:
    print("当前不是A股交易时间，生成模拟数据用于演示...")
    sleep_interval = 1   # 演示时等待1秒

# 15分钟内每隔一定时间爬取一次数据
for i in range(16):  # 0-15分钟，共16个数据点
    current_time = datetime.now() + timedelta(minutes=i if not is_trading_time else 0)
    print(f"\n第 {i+1} 次数据爬取 - {current_time.strftime('%H:%M:%S')}")
    
    # 爬取所有股票数据
    for stock_code, stock_name in stocks.items():
        data = get_stock_data(stock_code, base_prices, i)
        if data:
            data['timestamp'] = current_time
            data['minute'] = i
            all_data.append(data)
            print(f"{stock_name}({stock_code}): 价格={data['current_price']}, 涨跌率={data['change_rate']}%")
        else:
            print(f"获取 {stock_name}({stock_code}) 数据失败")
    
    # 等待指定时间（最后一次不需要等待）
    if i < 15:
        if is_trading_time:
            print("等待1分钟...")
            time.sleep(sleep_interval)
        else:
            print("等待1秒（演示模式）...")
            time.sleep(sleep_interval)

print("\n数据爬取完成！")

# 将数据转换为DataFrame并保存到CSV文件
if all_data:
    df = pd.DataFrame(all_data)
    
    # 保存到CSV文件
    df.to_csv('stock_data.csv', index=False, encoding='utf-8-sig')
    print(f"数据已保存到 stock_data.csv，共 {len(df)} 条记录")
    
    # 显示数据概览
    print("\n数据概览:")
    print(df.head(10))
    
    print("\n数据统计:")
    print(f"股票数量: {df['stock_code'].nunique()}")
    print(f"时间点数量: {df['minute'].nunique()}")
    print(f"总记录数: {len(df)}")
else:
    print("没有数据可保存")

# 从CSV文件读取数据
df = pd.read_csv('stock_data.csv')
df['timestamp'] = pd.to_datetime(df['timestamp'])

print("从CSV文件读取的数据:")
print(df.head())

# 1. 绘制5支股票涨跌率对比折线图
plt.figure(figsize=(12, 8))

for stock_code in stocks.keys():
    stock_data = df[df['stock_code'] == stock_code]
    stock_name = stocks[stock_code]
    
    plt.plot(stock_data['minute'], stock_data['change_rate'], 
             marker='o', linewidth=2, label=f'{stock_name}({stock_code})')

plt.title('5支A股股票15分钟涨跌率变化对比', fontsize=16, fontweight='bold')
plt.xlabel('时间 (分钟)', fontsize=12)
plt.ylabel('涨跌率 (%)', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, alpha=0.3)
plt.xticks(range(0, 16, 2))

# 添加零线
plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)

plt.tight_layout()
plt.savefig('股票涨跌率对比图.png', dpi=300, bbox_inches='tight')
plt.show()

# 2. 分别绘制5支股票的价格变化折线图
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

for i, (stock_code, stock_name) in enumerate(stocks.items()):
    stock_data = df[df['stock_code'] == stock_code]
    
    ax = axes[i]
    ax.plot(stock_data['minute'], stock_data['current_price'], 
            marker='o', linewidth=2, color=f'C{i}', markersize=4)
    
    ax.set_title(f'{stock_name}({stock_code}) 价格变化', fontsize=14, fontweight='bold')
    ax.set_xlabel('时间 (分钟)', fontsize=10)
    ax.set_ylabel('价格 (元)', fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_xticks(range(0, 16, 3))
    
    # 设置y轴刻度，突出价格变化
    price_min = stock_data['current_price'].min()
    price_max = stock_data['current_price'].max()
    price_range = price_max - price_min
    
    if price_range > 0:
        ax.set_ylim(price_min - price_range * 0.1, price_max + price_range * 0.1)
    
    # 添加价格范围信息
    ax.text(0.02, 0.98, f'最高: {price_max:.2f}\n最低: {price_min:.2f}\n波动: {price_range:.2f}', 
            transform=ax.transAxes, verticalalignment='top', 
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
            fontsize=8)

# 隐藏多余的子图
axes[5].set_visible(False)

plt.suptitle('5支A股股票15分钟价格变化详情', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('股票价格变化详情图.png', dpi=300, bbox_inches='tight')
plt.show()

# 生成数据分析报告
print("=" * 50)
print("A股股票数据分析报告")
print("=" * 50)

for stock_code, stock_name in stocks.items():
    stock_data = df[df['stock_code'] == stock_code]
    
    if len(stock_data) > 0:
        initial_price = stock_data.iloc[0]['current_price']
        final_price = stock_data.iloc[-1]['current_price']
        max_price = stock_data['current_price'].max()
        min_price = stock_data['current_price'].min()
        max_change_rate = stock_data['change_rate'].max()
        min_change_rate = stock_data['change_rate'].min()
        
        total_change = ((final_price - initial_price) / initial_price) * 100
        
        print(f"\n{stock_name}({stock_code}):")
        print(f"  起始价格: {initial_price:.2f} 元")
        print(f"  结束价格: {final_price:.2f} 元")
        print(f"  最高价格: {max_price:.2f} 元")
        print(f"  最低价格: {min_price:.2f} 元")
        print(f"  15分钟总涨跌: {total_change:.2f}%")
        print(f"  最大涨跌率: {max_change_rate:.2f}%")
        print(f"  最小涨跌率: {min_change_rate:.2f}%")
        print(f"  价格波动幅度: {max_price - min_price:.2f} 元")

print("\n" + "=" * 50)
print("实验完成！")
print("生成文件:")
print("- stock_data.csv: 股票数据")
print("- 股票涨跌率对比图.png: 涨跌率对比图")
print("- 股票价格变化详情图.png: 价格变化详情图")
print("=" * 50)