{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# A股股票数据爬取与可视化分析\n", "\n", "## 实验目标\n", "1. 在A股交易时间段，爬取5支股票15分钟内的交易价格数据和涨跌率\n", "2. 将数据保存到CSV文件\n", "3. 绘制涨跌率对比折线图和价格变化折线图\n", "\n", "## 选择的股票\n", "- 000001.SZ 平安银行\n", "- 600519.SH 贵州茅台\n", "- 002594.SZ 比亚迪\n", "- 000858.SZ 五粮液\n", "- 600036.SH 招商银行\n", "\n", "## A股交易时间\n", "工作日 9:00-11:30、13:00-15:00"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import requests\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "from datetime import datetime, timedelta\n", "import time\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义股票代码和名称\n", "stocks = {\n", "    '000001': '平安银行',\n", "    '600519': '贵州茅台', \n", "    '002594': '比亚迪',\n", "    '000858': '五粮液',\n", "    '600036': '招商银行'\n", "}\n", "\n", "# 数据存储列表\n", "all_data = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_stock_data(stock_code, base_prices=None, minute=0):\n", "    \"\"\"\n", "    获取股票实时数据\n", "    优先尝试API获取，失败时使用模拟数据\n", "    \"\"\"\n", "    try:\n", "        # 首先尝试API获取\n", "        if stock_code.startswith('6'):\n", "            market = 'sh'  # 上海证券交易所\n", "        else:\n", "            market = 'sz'  # 深圳证券交易所\n", "        \n", "        # 新浪财经API\n", "        url = f'http://hq.sinajs.cn/list={market}{stock_code}'\n", "        \n", "        headers = {\n", "            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "        }\n", "        \n", "        response = requests.get(url, headers=headers, timeout=5)\n", "        response.encoding = 'gbk'\n", "        \n", "        if response.status_code == 200 and 'var hq_str_' in response.text:\n", "            data_str = response.text\n", "            data_part = data_str.split('\"')[1]\n", "            data_list = data_part.split(',')\n", "            \n", "            if len(data_list) >= 32 and data_list[0]:  # 确保有股票名称\n", "                stock_name = data_list[0]\n", "                current_price = float(data_list[3])  # 当前价格\n", "                yesterday_close = float(data_list[2])  # 昨日收盘价\n", "                \n", "                if current_price > 0 and yesterday_close > 0:  # 确保价格有效\n", "                    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100\n", "                    \n", "                    return {\n", "                        'stock_code': stock_code,\n", "                        'stock_name': stock_name,\n", "                        'current_price': current_price,\n", "                        'yesterday_close': yesterday_close,\n", "                        'change_rate': round(change_rate, 2)\n", "                    }\n", "        \n", "        # API失败时使用模拟数据\n", "        print(f\"API获取失败，使用模拟数据: {stock_code}\")\n", "        return generate_mock_data(stock_code, base_prices, minute)\n", "        \n", "    except Exception as e:\n", "        print(f\"获取股票 {stock_code} 数据失败: {e}，使用模拟数据\")\n", "        return generate_mock_data(stock_code, base_prices, minute)\n", "\n", "def generate_mock_data(stock_code, base_prices=None, minute=0):\n", "    \"\"\"\n", "    生成模拟股票数据\n", "    \"\"\"\n", "    if base_prices is None:\n", "        base_prices = {'000001': 12.5, '600519': 1680.0, '002594': 280.0, '000858': 180.0, '600036': 45.0}\n", "    \n", "    stock_name = stocks[stock_code]\n", "    base_price = base_prices[stock_code]\n", "    \n", "    # 生成更真实的股票价格波动\n", "    np.random.seed(int(time.time()) + int(stock_code) + minute)\n", "    \n", "    # 基础波动率\n", "    volatility = 0.02  # 2%的波动率\n", "    \n", "    # 生成随机变化\n", "    random_change = np.random.normal(0, volatility)\n", "    \n", "    # 添加一些趋势性\n", "    trend_factor = np.sin(minute * 0.3) * 0.005  # 轻微的周期性趋势\n", "    \n", "    # 计算当前价格\n", "    price_change_rate = random_change + trend_factor\n", "    current_price = base_price * (1 + price_change_rate)\n", "    \n", "    yesterday_close = base_price\n", "    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100\n", "    \n", "    return {\n", "        'stock_code': stock_code,\n", "        'stock_name': stock_name,\n", "        'current_price': round(current_price, 2),\n", "        'yesterday_close': yesterday_close,\n", "        'change_rate': round(change_rate, 2)\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_trading_time():\n", "    \"\"\"\n", "    检查当前是否为A股交易时间\n", "    A股交易时间：工作日 9:00-11:30、13:00-15:00\n", "    \"\"\"\n", "    now = datetime.now()\n", "    \n", "    # 检查是否为工作日（周一到周五）\n", "    if now.weekday() >= 5:  # 周六、周日\n", "        return False\n", "    \n", "    current_time = now.time()\n", "    \n", "    # 上午交易时间 9:00-11:30\n", "    morning_start = datetime.strptime('09:00', '%H:%M').time()\n", "    morning_end = datetime.strptime('11:30', '%H:%M').time()\n", "    \n", "    # 下午交易时间 13:00-15:00\n", "    afternoon_start = datetime.strptime('13:00', '%H:%M').time()\n", "    afternoon_end = datetime.strptime('15:00', '%H:%M').time()\n", "    \n", "    return (morning_start <= current_time <= morning_end) or \\\n", "           (afternoon_start <= current_time <= afternoon_end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基础价格设置\n", "base_prices = {'000001': 12.5, '600519': 1680.0, '002594': 280.0, '000858': 180.0, '600036': 45.0}\n", "\n", "# 检查当前是否为交易时间\n", "is_trading_time = check_trading_time()\n", "\n", "if is_trading_time:\n", "    print(\"当前为A股交易时间，开始爬取实时数据...\")\n", "    sleep_interval = 60  # 实际爬取时等待1分钟\n", "else:\n", "    print(\"当前不是A股交易时间，生成模拟数据用于演示...\")\n", "    sleep_interval = 1   # 演示时等待1秒\n", "\n", "# 15分钟内每隔一定时间爬取一次数据\n", "for i in range(16):  # 0-15分钟，共16个数据点\n", "    current_time = datetime.now() + <PERSON><PERSON><PERSON>(minutes=i if not is_trading_time else 0)\n", "    print(f\"\\n第 {i+1} 次数据爬取 - {current_time.strftime('%H:%M:%S')}\")\n", "    \n", "    # 爬取所有股票数据\n", "    for stock_code, stock_name in stocks.items():\n", "        data = get_stock_data(stock_code, base_prices, i)\n", "        if data:\n", "            data['timestamp'] = current_time\n", "            data['minute'] = i\n", "            all_data.append(data)\n", "            print(f\"{stock_name}({stock_code}): 价格={data['current_price']}, 涨跌率={data['change_rate']}%\")\n", "        else:\n", "            print(f\"获取 {stock_name}({stock_code}) 数据失败\")\n", "    \n", "    # 等待指定时间（最后一次不需要等待）\n", "    if i < 15:\n", "        if is_trading_time:\n", "            print(\"等待1分钟...\")\n", "            time.sleep(sleep_interval)\n", "        else:\n", "            print(\"等待1秒（演示模式）...\")\n", "            time.sleep(sleep_interval)\n", "\n", "print(\"\\n数据爬取完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据保存"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将数据转换为DataFrame并保存到CSV文件\n", "if all_data:\n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # 保存到CSV文件\n", "    df.to_csv('stock_data.csv', index=False, encoding='utf-8-sig')\n", "    print(f\"数据已保存到 stock_data.csv，共 {len(df)} 条记录\")\n", "    \n", "    # 显示数据概览\n", "    print(\"\\n数据概览:\")\n", "    print(df.head(10))\n", "    \n", "    print(\"\\n数据统计:\")\n", "    print(f\"股票数量: {df['stock_code'].nunique()}\")\n", "    print(f\"时间点数量: {df['minute'].nunique()}\")\n", "    print(f\"总记录数: {len(df)}\")\n", "else:\n", "    print(\"没有数据可保存\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据可视化分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从CSV文件读取数据\n", "df = pd.read_csv('stock_data.csv')\n", "df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "\n", "print(\"从CSV文件读取的数据:\")\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. 绘制5支股票涨跌率对比折线图\n", "plt.figure(figsize=(12, 8))\n", "\n", "for stock_code in stocks.keys():\n", "    stock_data = df[df['stock_code'] == stock_code]\n", "    stock_name = stocks[stock_code]\n", "    \n", "    plt.plot(stock_data['minute'], stock_data['change_rate'], \n", "             marker='o', linewidth=2, label=f'{stock_name}({stock_code})')\n", "\n", "plt.title('5支A股股票15分钟涨跌率变化对比', fontsize=16, fontweight='bold')\n", "plt.xlabel('时间 (分钟)', fontsize=12)\n", "plt.ylabel('涨跌率 (%)', fontsize=12)\n", "plt.legend(fontsize=10)\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(range(0, 16, 2))\n", "\n", "# 添加零线\n", "plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)\n", "\n", "plt.tight_layout()\n", "plt.savefig('股票涨跌率对比图.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 分别绘制5支股票的价格变化折线图\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "for i, (stock_code, stock_name) in enumerate(stocks.items()):\n", "    stock_data = df[df['stock_code'] == stock_code]\n", "    \n", "    ax = axes[i]\n", "    ax.plot(stock_data['minute'], stock_data['current_price'], \n", "            marker='o', linewidth=2, color=f'C{i}', markersize=4)\n", "    \n", "    ax.set_title(f'{stock_name}({stock_code}) 价格变化', fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('时间 (分钟)', fontsize=10)\n", "    ax.set_ylabel('价格 (元)', fontsize=10)\n", "    ax.grid(True, alpha=0.3)\n", "    ax.set_xticks(range(0, 16, 3))\n", "    \n", "    # 设置y轴刻度，突出价格变化\n", "    price_min = stock_data['current_price'].min()\n", "    price_max = stock_data['current_price'].max()\n", "    price_range = price_max - price_min\n", "    \n", "    if price_range > 0:\n", "        ax.set_ylim(price_min - price_range * 0.1, price_max + price_range * 0.1)\n", "    \n", "    # 添加价格范围信息\n", "    ax.text(0.02, 0.98, f'最高: {price_max:.2f}\\n最低: {price_min:.2f}\\n波动: {price_range:.2f}', \n", "            transform=ax.transAxes, verticalalignment='top', \n", "            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),\n", "            fontsize=8)\n", "\n", "# 隐藏多余的子图\n", "axes[5].set_visible(False)\n", "\n", "plt.suptitle('5支A股股票15分钟价格变化详情', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.savefig('股票价格变化详情图.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据分析总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成数据分析报告\n", "print(\"=\" * 50)\n", "print(\"A股股票数据分析报告\")\n", "print(\"=\" * 50)\n", "\n", "for stock_code, stock_name in stocks.items():\n", "    stock_data = df[df['stock_code'] == stock_code]\n", "    \n", "    if len(stock_data) > 0:\n", "        initial_price = stock_data.iloc[0]['current_price']\n", "        final_price = stock_data.iloc[-1]['current_price']\n", "        max_price = stock_data['current_price'].max()\n", "        min_price = stock_data['current_price'].min()\n", "        max_change_rate = stock_data['change_rate'].max()\n", "        min_change_rate = stock_data['change_rate'].min()\n", "        \n", "        total_change = ((final_price - initial_price) / initial_price) * 100\n", "        \n", "        print(f\"\\n{stock_name}({stock_code}):\")\n", "        print(f\"  起始价格: {initial_price:.2f} 元\")\n", "        print(f\"  结束价格: {final_price:.2f} 元\")\n", "        print(f\"  最高价格: {max_price:.2f} 元\")\n", "        print(f\"  最低价格: {min_price:.2f} 元\")\n", "        print(f\"  15分钟总涨跌: {total_change:.2f}%\")\n", "        print(f\"  最大涨跌率: {max_change_rate:.2f}%\")\n", "        print(f\"  最小涨跌率: {min_change_rate:.2f}%\")\n", "        print(f\"  价格波动幅度: {max_price - min_price:.2f} 元\")\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"实验完成！\")\n", "print(\"生成文件:\")\n", "print(\"- stock_data.csv: 股票数据\")\n", "print(\"- 股票涨跌率对比图.png: 涨跌率对比图\")\n", "print(\"- 股票价格变化详情图.png: 价格变化详情图\")\n", "print(\"=\" * 50)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}