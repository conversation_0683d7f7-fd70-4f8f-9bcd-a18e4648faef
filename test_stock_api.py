#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票API连接
"""

import requests
import time
from datetime import datetime

def test_stock_api():
    """测试股票API是否可用"""
    
    # 测试股票代码
    test_stocks = ['000001', '600519']
    
    print("测试股票API连接...")
    print("=" * 40)
    
    for stock_code in test_stocks:
        try:
            # 判断股票代码所属市场
            if stock_code.startswith('6'):
                market = 'sh'  # 上海证券交易所
            else:
                market = 'sz'  # 深圳证券交易所
            
            # 新浪财经API
            url = f'http://hq.sinajs.cn/list={market}{stock_code}'
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.encoding = 'gbk'
            
            if response.status_code == 200:
                data_str = response.text
                print(f"股票 {stock_code} API响应: {response.status_code}")
                
                # 解析数据
                if 'var hq_str_' in data_str:
                    data_part = data_str.split('"')[1]
                    data_list = data_part.split(',')
                    
                    if len(data_list) >= 32:
                        stock_name = data_list[0]
                        current_price = float(data_list[3])  # 当前价格
                        yesterday_close = float(data_list[2])  # 昨日收盘价
                        
                        # 计算涨跌率
                        change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
                        
                        print(f"  股票名称: {stock_name}")
                        print(f"  当前价格: {current_price} 元")
                        print(f"  昨日收盘: {yesterday_close} 元")
                        print(f"  涨跌率: {change_rate:.2f}%")
                        print(f"  ✓ 数据获取成功")
                    else:
                        print(f"  ✗ 数据格式异常")
                else:
                    print(f"  ✗ 无有效数据")
            else:
                print(f"  ✗ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ 获取股票 {stock_code} 数据失败: {e}")
        
        print("-" * 40)
        time.sleep(1)  # 避免请求过于频繁

if __name__ == "__main__":
    test_stock_api()
