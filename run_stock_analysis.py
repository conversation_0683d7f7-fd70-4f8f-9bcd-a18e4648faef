#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行股票数据分析的简化版本
用于测试和演示
"""

import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 定义股票代码和名称
stocks = {
    '000001': '平安银行',
    '600519': '贵州茅台', 
    '002594': '比亚迪',
    '000858': '五粮液',
    '600036': '招商银行'
}

# 基础价格设置
base_prices = {'000001': 12.5, '600519': 1680.0, '002594': 280.0, '000858': 180.0, '600036': 45.0}

def generate_mock_data(stock_code, minute=0):
    """生成模拟股票数据"""
    stock_name = stocks[stock_code]
    base_price = base_prices[stock_code]
    
    # 生成更真实的股票价格波动
    np.random.seed(int(time.time()) + int(stock_code) + minute)
    
    # 基础波动率
    volatility = 0.02  # 2%的波动率
    
    # 生成随机变化
    random_change = np.random.normal(0, volatility)
    
    # 添加一些趋势性
    trend_factor = np.sin(minute * 0.3) * 0.005  # 轻微的周期性趋势
    
    # 计算当前价格
    price_change_rate = random_change + trend_factor
    current_price = base_price * (1 + price_change_rate)
    
    yesterday_close = base_price
    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
    
    return {
        'stock_code': stock_code,
        'stock_name': stock_name,
        'current_price': round(current_price, 2),
        'yesterday_close': yesterday_close,
        'change_rate': round(change_rate, 2)
    }

def collect_stock_data():
    """收集股票数据"""
    all_data = []
    
    print("开始生成股票数据...")
    
    # 生成16个时间点的数据
    for i in range(16):
        current_time = datetime.now() + timedelta(minutes=i)
        print(f"\n第 {i+1} 次数据收集 - {current_time.strftime('%H:%M:%S')}")
        
        # 收集所有股票数据
        for stock_code, stock_name in stocks.items():
            data = generate_mock_data(stock_code, i)
            data['timestamp'] = current_time
            data['minute'] = i
            all_data.append(data)
            print(f"{stock_name}({stock_code}): 价格={data['current_price']}, 涨跌率={data['change_rate']}%")
        
        time.sleep(0.1)  # 短暂延迟
    
    return all_data

def save_data(all_data):
    """保存数据到CSV"""
    if all_data:
        df = pd.DataFrame(all_data)
        df.to_csv('stock_data.csv', index=False, encoding='utf-8-sig')
        print(f"\n数据已保存到 stock_data.csv，共 {len(df)} 条记录")
        return df
    return None

def create_visualizations(df):
    """创建可视化图表"""
    if df is None or len(df) == 0:
        print("没有数据可用于可视化")
        return
    
    # 1. 绘制5支股票涨跌率对比折线图
    plt.figure(figsize=(12, 8))
    
    for stock_code in stocks.keys():
        stock_data = df[df['stock_code'] == stock_code]
        stock_name = stocks[stock_code]
        
        plt.plot(stock_data['minute'], stock_data['change_rate'], 
                 marker='o', linewidth=2, label=f'{stock_name}({stock_code})')
    
    plt.title('5支A股股票15分钟涨跌率变化对比', fontsize=16, fontweight='bold')
    plt.xlabel('时间 (分钟)', fontsize=12)
    plt.ylabel('涨跌率 (%)', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.xticks(range(0, 16, 2))
    
    # 添加零线
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('股票涨跌率对比图.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 分别绘制5支股票的价格变化折线图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, (stock_code, stock_name) in enumerate(stocks.items()):
        stock_data = df[df['stock_code'] == stock_code]
        
        ax = axes[i]
        ax.plot(stock_data['minute'], stock_data['current_price'], 
                marker='o', linewidth=2, color=f'C{i}', markersize=4)
        
        ax.set_title(f'{stock_name}({stock_code}) 价格变化', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=10)
        ax.set_ylabel('价格 (元)', fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.set_xticks(range(0, 16, 3))
        
        # 设置y轴刻度，突出价格变化
        price_min = stock_data['current_price'].min()
        price_max = stock_data['current_price'].max()
        price_range = price_max - price_min
        
        if price_range > 0:
            ax.set_ylim(price_min - price_range * 0.1, price_max + price_range * 0.1)
        
        # 添加价格范围信息
        ax.text(0.02, 0.98, f'最高: {price_max:.2f}\n最低: {price_min:.2f}\n波动: {price_range:.2f}', 
                transform=ax.transAxes, verticalalignment='top', 
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=8)
    
    # 隐藏多余的子图
    axes[5].set_visible(False)
    
    plt.suptitle('5支A股股票15分钟价格变化详情', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('股票价格变化详情图.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_report(df):
    """生成数据分析报告"""
    if df is None or len(df) == 0:
        print("没有数据可用于分析")
        return
    
    print("=" * 50)
    print("A股股票数据分析报告")
    print("=" * 50)
    
    for stock_code, stock_name in stocks.items():
        stock_data = df[df['stock_code'] == stock_code]
        
        if len(stock_data) > 0:
            initial_price = stock_data.iloc[0]['current_price']
            final_price = stock_data.iloc[-1]['current_price']
            max_price = stock_data['current_price'].max()
            min_price = stock_data['current_price'].min()
            max_change_rate = stock_data['change_rate'].max()
            min_change_rate = stock_data['change_rate'].min()
            
            total_change = ((final_price - initial_price) / initial_price) * 100
            
            print(f"\n{stock_name}({stock_code}):")
            print(f"  起始价格: {initial_price:.2f} 元")
            print(f"  结束价格: {final_price:.2f} 元")
            print(f"  最高价格: {max_price:.2f} 元")
            print(f"  最低价格: {min_price:.2f} 元")
            print(f"  15分钟总涨跌: {total_change:.2f}%")
            print(f"  最大涨跌率: {max_change_rate:.2f}%")
            print(f"  最小涨跌率: {min_change_rate:.2f}%")
            print(f"  价格波动幅度: {max_price - min_price:.2f} 元")
    
    print("\n" + "=" * 50)
    print("实验完成！")
    print("生成文件:")
    print("- stock_data.csv: 股票数据")
    print("- 股票涨跌率对比图.png: 涨跌率对比图")
    print("- 股票价格变化详情图.png: 价格变化详情图")
    print("=" * 50)

def main():
    """主函数"""
    print("A股股票数据爬取与可视化分析")
    print("=" * 40)
    
    # 1. 收集数据
    all_data = collect_stock_data()
    
    # 2. 保存数据
    df = save_data(all_data)
    
    # 3. 创建可视化
    create_visualizations(df)
    
    # 4. 生成报告
    generate_report(df)

if __name__ == "__main__":
    main()
