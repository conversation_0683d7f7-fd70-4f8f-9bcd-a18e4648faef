#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将Jupyter notebook转换为PDF
"""

import subprocess
import sys
import os

def convert_notebook_to_pdf():
    """将notebook转换为PDF"""
    
    notebook_file = "stock_crawler.ipynb"
    
    if not os.path.exists(notebook_file):
        print(f"错误: 找不到文件 {notebook_file}")
        return False
    
    try:
        print("正在将notebook转换为PDF...")
        
        # 尝试使用nbconvert转换为PDF
        cmd = ["jupyter", "nbconvert", "--to", "pdf", notebook_file]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ PDF转换成功！")
            print(f"生成文件: {notebook_file.replace('.ipynb', '.pdf')}")
            return True
        else:
            print("✗ PDF转换失败，尝试转换为HTML...")
            print(f"错误信息: {result.stderr}")
            
            # 如果PDF转换失败，尝试转换为HTML
            cmd_html = ["jupyter", "nbconvert", "--to", "html", notebook_file]
            result_html = subprocess.run(cmd_html, capture_output=True, text=True)
            
            if result_html.returncode == 0:
                print("✓ HTML转换成功！")
                print(f"生成文件: {notebook_file.replace('.ipynb', '.html')}")
                print("提示: 您可以在浏览器中打开HTML文件，然后打印为PDF")
                return True
            else:
                print("✗ HTML转换也失败了")
                print(f"错误信息: {result_html.stderr}")
                return False
                
    except FileNotFoundError:
        print("错误: 找不到jupyter命令，请确保已安装Jupyter")
        print("安装命令: pip install jupyter nbconvert")
        return False
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("Jupyter Notebook PDF转换工具")
    print("=" * 40)
    
    success = convert_notebook_to_pdf()
    
    if success:
        print("\n转换完成！")
    else:
        print("\n转换失败，请检查错误信息并重试")
        print("\n备选方案:")
        print("1. 在Jupyter Notebook中打开文件")
        print("2. 选择 File -> Download as -> PDF via LaTeX")
        print("3. 或者选择 File -> Download as -> HTML，然后在浏览器中打印为PDF")

if __name__ == "__main__":
    main()
