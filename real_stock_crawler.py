#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实A股股票数据爬取与可视化分析
使用多个数据源确保获取真实数据
"""

import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import time
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 定义股票代码和名称
stocks = {
    '000001': '平安银行',
    '600519': '贵州茅台', 
    '002594': '比亚迪',
    '000858': '五粮液',
    '600036': '招商银行'
}

def get_stock_data_from_sina(stock_code):
    """从新浪财经获取股票数据"""
    try:
        if stock_code.startswith('6'):
            market = 'sh'  # 上海证券交易所
        else:
            market = 'sz'  # 深圳证券交易所
        
        url = f'http://hq.sinajs.cn/list={market}{stock_code}'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'http://finance.sina.com.cn/',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'gbk'
        
        if response.status_code == 200 and 'var hq_str_' in response.text:
            data_str = response.text
            data_part = data_str.split('"')[1]
            data_list = data_part.split(',')
            
            if len(data_list) >= 32 and data_list[0]:  # 确保有股票名称
                stock_name = data_list[0]
                current_price = float(data_list[3])  # 当前价格
                yesterday_close = float(data_list[2])  # 昨日收盘价
                
                if current_price > 0 and yesterday_close > 0:  # 确保价格有效
                    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
                    
                    return {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'current_price': current_price,
                        'yesterday_close': yesterday_close,
                        'change_rate': round(change_rate, 2),
                        'data_source': 'sina'
                    }
        return None
    except Exception as e:
        print(f"新浪财经API失败: {e}")
        return None

def get_stock_data_from_tencent(stock_code):
    """从腾讯财经获取股票数据"""
    try:
        if stock_code.startswith('6'):
            symbol = f'sh{stock_code}'  # 上海证券交易所
        else:
            symbol = f'sz{stock_code}'  # 深圳证券交易所
        
        url = f'http://qt.gtimg.cn/q={symbol}'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://gu.qq.com/',
            'Accept': '*/*'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'gbk'
        
        if response.status_code == 200:
            data_str = response.text
            if f'v_{symbol}=' in data_str:
                data_part = data_str.split('"')[1]
                data_list = data_part.split('~')
                
                if len(data_list) >= 50:
                    stock_name = data_list[1]
                    current_price = float(data_list[3])  # 当前价格
                    yesterday_close = float(data_list[4])  # 昨日收盘价
                    
                    if current_price > 0 and yesterday_close > 0:
                        change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
                        
                        return {
                            'stock_code': stock_code,
                            'stock_name': stock_name,
                            'current_price': current_price,
                            'yesterday_close': yesterday_close,
                            'change_rate': round(change_rate, 2),
                            'data_source': 'tencent'
                        }
        return None
    except Exception as e:
        print(f"腾讯财经API失败: {e}")
        return None

def get_stock_data_from_eastmoney(stock_code):
    """从东方财富获取股票数据"""
    try:
        if stock_code.startswith('6'):
            symbol = f'1.{stock_code}'  # 上海证券交易所
        else:
            symbol = f'0.{stock_code}'  # 深圳证券交易所
        
        url = f'http://push2.eastmoney.com/api/qt/stock/get?secid={symbol}&fields=f43,f44,f45,f46,f47,f48,f57,f58'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': 'application/json'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and data['data']:
                stock_data = data['data']
                stock_name = stocks.get(stock_code, f'股票{stock_code}')
                current_price = float(stock_data.get('f43', 0)) / 100  # 当前价格（分转元）
                yesterday_close = float(stock_data.get('f60', 0)) / 100  # 昨日收盘价
                
                if current_price > 0 and yesterday_close > 0:
                    change_rate = ((current_price - yesterday_close) / yesterday_close) * 100
                    
                    return {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'current_price': current_price,
                        'yesterday_close': yesterday_close,
                        'change_rate': round(change_rate, 2),
                        'data_source': 'eastmoney'
                    }
        return None
    except Exception as e:
        print(f"东方财富API失败: {e}")
        return None

def get_stock_data(stock_code):
    """获取股票实时数据 - 尝试多个数据源"""
    print(f"正在获取 {stocks[stock_code]}({stock_code}) 的实时数据...")
    
    # 按优先级尝试不同的数据源
    data_sources = [
        ('新浪财经', get_stock_data_from_sina),
        ('腾讯财经', get_stock_data_from_tencent),
        ('东方财富', get_stock_data_from_eastmoney)
    ]
    
    for source_name, get_func in data_sources:
        try:
            print(f"  尝试从{source_name}获取数据...")
            data = get_func(stock_code)
            if data:
                print(f"  ✓ 成功从{source_name}获取数据")
                print(f"    价格: {data['current_price']} 元, 涨跌率: {data['change_rate']}%")
                return data
            else:
                print(f"  ✗ {source_name}无有效数据")
        except Exception as e:
            print(f"  ✗ {source_name}获取失败: {e}")
        
        time.sleep(1)  # 避免请求过于频繁
    
    print(f"  ⚠️ 所有数据源都失败，跳过 {stock_code}")
    return None

def check_trading_time():
    """检查当前是否为A股交易时间"""
    now = datetime.now()
    
    # 检查是否为工作日（周一到周五）
    if now.weekday() >= 5:  # 周六、周日
        return False
    
    current_time = now.time()
    
    # 上午交易时间 9:00-11:30
    morning_start = datetime.strptime('09:00', '%H:%M').time()
    morning_end = datetime.strptime('11:30', '%H:%M').time()
    
    # 下午交易时间 13:00-15:00
    afternoon_start = datetime.strptime('13:00', '%H:%M').time()
    afternoon_end = datetime.strptime('15:00', '%H:%M').time()
    
    return (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end)

def collect_real_stock_data():
    """收集真实股票数据"""
    all_data = []
    
    is_trading_time = check_trading_time()
    
    if is_trading_time:
        print("🔥 当前为A股交易时间，开始爬取真实数据...")
        sleep_interval = 60  # 实际爬取时等待1分钟
    else:
        print("⏰ 当前不是A股交易时间，但仍尝试获取最新数据...")
        sleep_interval = 5   # 非交易时间等待5秒
    
    print("=" * 60)
    
    # 15分钟内每隔一定时间爬取一次数据
    for i in range(16):  # 0-15分钟，共16个数据点
        current_time = datetime.now()
        print(f"\n📊 第 {i+1} 次数据爬取 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 50)
        
        # 爬取所有股票数据
        success_count = 0
        for stock_code, stock_name in stocks.items():
            data = get_stock_data(stock_code)
            if data:
                data['timestamp'] = current_time
                data['minute'] = i
                all_data.append(data)
                success_count += 1
            else:
                print(f"  ❌ 获取 {stock_name}({stock_code}) 数据失败")
            
            time.sleep(0.5)  # 每个股票之间短暂延迟
        
        print(f"\n本轮成功获取 {success_count}/{len(stocks)} 支股票数据")
        
        # 等待指定时间（最后一次不需要等待）
        if i < 15:
            print(f"⏳ 等待 {sleep_interval} 秒...")
            time.sleep(sleep_interval)
    
    print("\n" + "=" * 60)
    print(f"🎉 数据爬取完成！共获取 {len(all_data)} 条真实数据记录")

    return all_data

def save_data(all_data):
    """保存数据到CSV"""
    if all_data:
        df = pd.DataFrame(all_data)
        df.to_csv('real_stock_data.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 数据已保存到 real_stock_data.csv，共 {len(df)} 条记录")

        # 显示数据概览
        print("\n📋 数据概览:")
        print(df.head(10))

        # 显示数据来源统计
        if 'data_source' in df.columns:
            print("\n📊 数据来源统计:")
            source_counts = df['data_source'].value_counts()
            for source, count in source_counts.items():
                print(f"  {source}: {count} 条记录")

        print("\n📈 数据统计:")
        print(f"  股票数量: {df['stock_code'].nunique()}")
        print(f"  时间点数量: {df['minute'].nunique()}")
        print(f"  总记录数: {len(df)}")

        return df
    else:
        print("❌ 没有数据可保存")
        return None

def create_visualizations(df):
    """创建可视化图表"""
    if df is None or len(df) == 0:
        print("❌ 没有数据可用于可视化")
        return

    print("\n🎨 开始创建可视化图表...")

    # 1. 绘制5支股票涨跌率对比折线图
    plt.figure(figsize=(14, 8))

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

    for i, stock_code in enumerate(stocks.keys()):
        stock_data = df[df['stock_code'] == stock_code]
        if len(stock_data) > 0:
            stock_name = stocks[stock_code]

            plt.plot(stock_data['minute'], stock_data['change_rate'],
                     marker='o', linewidth=2.5, markersize=6,
                     color=colors[i % len(colors)],
                     label=f'{stock_name}({stock_code})')

    plt.title('5支A股股票15分钟涨跌率变化对比（真实数据）', fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('时间 (分钟)', fontsize=14)
    plt.ylabel('涨跌率 (%)', fontsize=14)
    plt.legend(fontsize=12, loc='best')
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.xticks(range(0, 16, 2), fontsize=12)
    plt.yticks(fontsize=12)

    # 添加零线
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.7, linewidth=1)

    # 添加数据来源信息
    if 'data_source' in df.columns:
        sources = df['data_source'].unique()
        source_text = f"数据来源: {', '.join(sources)}"
        plt.figtext(0.02, 0.02, source_text, fontsize=10, style='italic')

    plt.tight_layout()
    plt.savefig('真实股票涨跌率对比图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 分别绘制5支股票的价格变化折线图
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    axes = axes.flatten()

    for i, (stock_code, stock_name) in enumerate(stocks.items()):
        stock_data = df[df['stock_code'] == stock_code]

        if len(stock_data) > 0:
            ax = axes[i]
            ax.plot(stock_data['minute'], stock_data['current_price'],
                    marker='o', linewidth=3, color=colors[i % len(colors)],
                    markersize=6, markerfacecolor='white', markeredgewidth=2)

            ax.set_title(f'{stock_name}({stock_code}) 价格变化',
                        fontsize=16, fontweight='bold', pad=15)
            ax.set_xlabel('时间 (分钟)', fontsize=12)
            ax.set_ylabel('价格 (元)', fontsize=12)
            ax.grid(True, alpha=0.3, linestyle='--')
            ax.set_xticks(range(0, 16, 3))

            # 设置y轴刻度，突出价格变化
            price_min = stock_data['current_price'].min()
            price_max = stock_data['current_price'].max()
            price_range = price_max - price_min

            if price_range > 0:
                ax.set_ylim(price_min - price_range * 0.1, price_max + price_range * 0.1)

            # 添加价格范围信息
            info_text = f'最高: {price_max:.2f}\n最低: {price_min:.2f}\n波动: {price_range:.2f}'
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))

            # 添加数据来源
            if len(stock_data) > 0 and 'data_source' in stock_data.columns:
                source = stock_data.iloc[0]['data_source']
                ax.text(0.98, 0.02, f'数据源: {source}', transform=ax.transAxes,
                       horizontalalignment='right', fontsize=9, style='italic')

    # 隐藏多余的子图
    axes[5].set_visible(False)

    plt.suptitle('5支A股股票15分钟价格变化详情（真实数据）', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('真实股票价格变化详情图.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ 可视化图表创建完成！")

def generate_report(df):
    """生成数据分析报告"""
    if df is None or len(df) == 0:
        print("❌ 没有数据可用于分析")
        return

    print("\n" + "=" * 60)
    print("📊 A股真实股票数据分析报告")
    print("=" * 60)

    for stock_code, stock_name in stocks.items():
        stock_data = df[df['stock_code'] == stock_code]

        if len(stock_data) > 0:
            initial_price = stock_data.iloc[0]['current_price']
            final_price = stock_data.iloc[-1]['current_price']
            max_price = stock_data['current_price'].max()
            min_price = stock_data['current_price'].min()
            max_change_rate = stock_data['change_rate'].max()
            min_change_rate = stock_data['change_rate'].min()

            total_change = ((final_price - initial_price) / initial_price) * 100

            print(f"\n📈 {stock_name}({stock_code}):")
            print(f"  起始价格: {initial_price:.2f} 元")
            print(f"  结束价格: {final_price:.2f} 元")
            print(f"  最高价格: {max_price:.2f} 元")
            print(f"  最低价格: {min_price:.2f} 元")
            print(f"  15分钟总涨跌: {total_change:.2f}%")
            print(f"  最大涨跌率: {max_change_rate:.2f}%")
            print(f"  最小涨跌率: {min_change_rate:.2f}%")
            print(f"  价格波动幅度: {max_price - min_price:.2f} 元")

            # 显示数据来源
            if 'data_source' in stock_data.columns:
                sources = stock_data['data_source'].unique()
                print(f"  数据来源: {', '.join(sources)}")

    print("\n" + "=" * 60)
    print("🎉 真实数据分析完成！")
    print("📁 生成文件:")
    print("  - real_stock_data.csv: 真实股票数据")
    print("  - 真实股票涨跌率对比图.png: 涨跌率对比图")
    print("  - 真实股票价格变化详情图.png: 价格变化详情图")
    print("=" * 60)

def main():
    """主函数"""
    print("🚀 A股真实股票数据爬取与可视化分析")
    print("=" * 60)
    print("📋 选择的股票:")
    for code, name in stocks.items():
        print(f"  {name}({code})")
    print("=" * 60)

    # 1. 收集真实数据
    all_data = collect_real_stock_data()

    if not all_data:
        print("❌ 未能获取任何真实数据，请检查网络连接或稍后重试")
        return

    # 2. 保存数据
    df = save_data(all_data)

    # 3. 创建可视化
    create_visualizations(df)

    # 4. 生成报告
    generate_report(df)

if __name__ == "__main__":
    main()
